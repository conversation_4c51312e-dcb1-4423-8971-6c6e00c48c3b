# Schéma Fonctionnel - Application de Gestion de Stock

## Vue d'ensemble de l'Architecture

L'application suit une **architecture hexagonale (ports et adaptateurs)** avec une séparation claire entre les couches :

```
┌─────────────────────────────────────────────────────────────────┐
│                    COUCHE PRÉSENTATION                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   REST API      │  │   Swagger UI    │  │   Security      │ │
│  │   /api/v1       │  │   Documentation │  │   JWT + RBAC    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    COUCHE APPLICATION                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Controllers   │  │      DTOs       │  │     Mappers     │ │
│  │   Resources     │  │   Data Transfer │  │   Domain ↔ DTO  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                     COUCHE DOMAINE                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Use Cases     │  │   Domain Model  │  │   Validators    │ │
│  │   Services      │  │   Entities      │  │   Business      │ │
│  │   Ports (In)    │  │   Value Objects │  │   Rules         │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                  COUCHE INFRASTRUCTURE                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Adapters      │  │   JPA Entities  │  │   External      │ │
│  │   Repositories  │  │   Database      │  │   Services      │ │
│  │   Ports (Out)   │  │   Persistence   │  │   Flickr API    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Modules Fonctionnels Principaux

### 1. 📦 **Gestion des Produits (Product Management)**
- **Entités** : Product, Category
- **Fonctionnalités** :
  - Création, modification, suppression d'articles
  - Gestion des catégories
  - Consultation de l'historique des ventes par article
  - Gestion des prix (HT, TTC, TVA)

### 2. 👥 **Gestion des Clients (Customer Management)**
- **Entités** : Customer, CustomerOrder, LineCustomerOrder
- **Fonctionnalités** :
  - Gestion des clients (CRUD)
  - Création et suivi des commandes clients
  - Gestion des lignes de commande
  - Livraison des commandes

### 3. 🏭 **Gestion des Fournisseurs (Supplier Management)**
- **Entités** : Supplier, SupplierOrder, LineSupplierOrder
- **Fonctionnalités** :
  - Gestion des fournisseurs (CRUD)
  - Création et suivi des commandes fournisseurs
  - Gestion des lignes de commande
  - Réception des commandes

### 4. 💰 **Gestion des Ventes (Sales Management)**
- **Entités** : Sales, LineSales
- **Fonctionnalités** :
  - Enregistrement des ventes
  - Gestion des lignes de vente
  - Historique des ventes par article

### 5. 📊 **Gestion des Stocks (Inventory Management)**
- **Entités** : InventoryMovement
- **Fonctionnalités** :
  - Mouvements de stock (entrée, sortie)
  - Corrections de stock (positive, négative)
  - Calcul du stock réel
  - Historique des mouvements

### 6. 🏢 **Gestion des Entreprises (Company Management)**
- **Entités** : Company, User, Authority
- **Fonctionnalités** :
  - Création d'entreprises
  - Gestion multi-tenant
  - Gestion des utilisateurs et rôles

## Modèle de Données - Relations

```mermaid
erDiagram
    Company ||--o{ User : "employs"
    Company ||--o{ Product : "owns"
    Company ||--o{ Customer : "manages"
    Company ||--o{ Supplier : "works_with"
    
    User ||--o{ Authority : "has_roles"
    
    Category ||--o{ Product : "categorizes"
    Product ||--o{ LineSales : "sold_in"
    Product ||--o{ LineCustomerOrder : "ordered_in"
    Product ||--o{ LineSupplierOrder : "supplied_in"
    Product ||--o{ InventoryMovement : "moves"
    
    Customer ||--o{ CustomerOrder : "places"
    CustomerOrder ||--o{ LineCustomerOrder : "contains"
    
    Supplier ||--o{ SupplierOrder : "receives"
    SupplierOrder ||--o{ LineSupplierOrder : "contains"
    
    Sales ||--o{ LineSales : "includes"
    
    Company {
        Long id PK
        String nom
        String description
        Address adresse
        String codeFiscal
        String email
        String numTel
        String siteWeb
    }
    
    User {
        Long id PK
        String nom
        String prenom
        String email
        String motDePasse
        Address adresse
        Company entreprise FK
    }
    
    Product {
        Long id PK
        String codeArticle
        String designation
        BigDecimal prixUnitaireHt
        BigDecimal prixUnitaireTtc
        BigDecimal tauxTva
        String photo
        Long idEntreprise FK
        Category categorie FK
    }
    
    Customer {
        Long id PK
        String nom
        String prenom
        Address adresse
        String email
        String numTel
        Long idEntreprise FK
    }
    
    Supplier {
        Long id PK
        String nom
        String prenom
        Address adresse
        String email
        String numTel
        Long idEntreprise FK
    }
```

## API REST - Endpoints Principaux

### 🔐 **Authentification**
```
POST   /api/v1/authenticate          # Connexion utilisateur
POST   /api/v1/entreprises           # Création entreprise (public)
```

### 📦 **Articles/Produits**
```
POST   /api/v1/articles                           # Créer un article
GET    /api/v1/articles                           # Lister les articles
GET    /api/v1/articles/{id}/historiquevente      # Historique ventes
GET    /api/v1/articles/{id}/historiquecommandeclient    # Historique commandes client
GET    /api/v1/articles/{id}/historiquecommandefournisseur # Historique commandes fournisseur
DELETE /api/v1/articles/{id}                      # Supprimer un article
```

### 📊 **Mouvements de Stock**
```
GET    /api/v1/mvtstk/{idArticle}/stockreel       # Stock réel d'un article
GET    /api/v1/mvtstk/{idArticle}/article         # Mouvements d'un article
POST   /api/v1/mvtstk/entree                      # Entrée de stock
POST   /api/v1/mvtstk/sortie                      # Sortie de stock
POST   /api/v1/mvtstk/correctionpos               # Correction positive
POST   /api/v1/mvtstk/correctionneg               # Correction négative
```

### 👥 **Clients**
```
POST   /api/v1/clients                            # Créer un client
GET    /api/v1/clients                            # Lister les clients
DELETE /api/v1/clients/{id}                       # Supprimer un client
```

### 🛒 **Commandes Clients**
```
POST   /api/v1/commandesclients                   # Créer commande client
GET    /api/v1/commandesclients                   # Lister commandes
GET    /api/v1/commandesclients/{id}              # Détail commande
PATCH  /api/v1/commandesclients/{id}/etatcommande/{etat} # Changer état
POST   /api/v1/commandesclients/{id}/line/article # Ajouter article
DELETE /api/v1/commandesclients/{id}/line/{lineId}/article # Supprimer article
```

### 🏭 **Fournisseurs**
```
POST   /api/v1/fournisseurs                       # Créer fournisseur
GET    /api/v1/fournisseurs                       # Lister fournisseurs
DELETE /api/v1/fournisseurs/{id}                  # Supprimer fournisseur
```

### 📋 **Commandes Fournisseurs**
```
POST   /api/v1/commandesfournisseurs              # Créer commande fournisseur
GET    /api/v1/commandesfournisseurs              # Lister commandes
GET    /api/v1/commandesfournisseurs/{id}         # Détail commande
PATCH  /api/v1/commandesfournisseurs/{id}/etatcommande/{etat} # Changer état
POST   /api/v1/commandesfournisseurs/{id}/line/article # Ajouter article
DELETE /api/v1/commandesfournisseurs/{id}/line/{lineId}/article # Supprimer article
```

### 💰 **Ventes**
```
POST   /api/v1/ventes                             # Créer une vente
GET    /api/v1/ventes                             # Lister les ventes
DELETE /api/v1/ventes/{id}                        # Supprimer une vente
```

### 🏷️ **Catégories**
```
POST   /api/v1/categories                         # Créer catégorie
GET    /api/v1/categories                         # Lister catégories
DELETE /api/v1/categories/{id}                    # Supprimer catégorie
```

### 👤 **Utilisateurs**
```
POST   /api/v1/utilisateurs                       # Créer utilisateur
GET    /api/v1/utilisateurs                       # Lister utilisateurs (ADMIN)
GET    /api/v1/utilisateurs/account               # Profil utilisateur connecté
PUT    /api/v1/utilisateurs/{id}/password         # Changer mot de passe
```

## Flux Fonctionnels Principaux

### 🔄 **Flux de Commande Client**
```
1. Création Client → 2. Création Commande → 3. Ajout Articles → 
4. Validation → 5. Livraison → 6. Mise à jour Stock
```

### 🔄 **Flux de Commande Fournisseur**
```
1. Création Fournisseur → 2. Création Commande → 3. Ajout Articles → 
4. Validation → 5. Réception → 6. Mise à jour Stock
```

### 🔄 **Flux de Vente**
```
1. Sélection Articles → 2. Création Vente → 3. Enregistrement → 
4. Sortie Stock Automatique
```

### 🔄 **Flux de Gestion Stock**
```
1. Mouvement Manuel → 2. Validation → 3. Mise à jour Stock Réel → 
4. Historique
```

## Types d'Énumérations

### **États des Commandes (OrderStatus)**
- `EN_PREPARATION` - Commande en préparation
- `VALIDEE` - Commande validée
- `LIVREE` - Commande livrée

### **Types de Mouvements de Stock (TypeInventoryMovement)**
- `ENTREE` - Entrée de stock
- `SORTIE` - Sortie de stock
- `CORRECTION_POS` - Correction positive
- `CORRECTION_NEG` - Correction négative

### **Sources de Mouvements (SourceInventoryMovement)**
- `COMMANDE_CLIENT` - Mouvement lié à une commande client
- `COMMANDE_FOURNISSEUR` - Mouvement lié à une commande fournisseur
- `VENTE` - Mouvement lié à une vente

## Sécurité et Authentification

### **Mécanisme d'Authentification**
- **JWT (JSON Web Token)** pour l'authentification stateless
- **BCrypt** pour le hachage des mots de passe
- **CORS** configuré pour les appels cross-origin

### **Gestion des Rôles**
- **ADMIN** : Accès complet à toutes les fonctionnalités
- **USER** : Accès limité selon les permissions

### **Multi-tenant**
- Chaque entreprise est isolée par `idEntreprise`
- Les données sont filtrées automatiquement par entreprise

## Technologies Utilisées

- **Backend** : Spring Boot, Spring Security, Spring Data JPA
- **Base de données** : JPA/Hibernate avec base relationnelle
- **Documentation** : OpenAPI 3 / Swagger
- **Tests** : Cucumber pour les tests BDD
- **Mapping** : MapStruct pour la conversion Domain ↔ DTO
- **Validation** : Validators personnalisés pour les règles métier

## Points d'Extension

1. **Intégration Flickr** : Gestion des photos d'articles
2. **Notifications** : Système d'alertes pour les stocks faibles
3. **Rapports** : Génération de rapports de vente et stock
4. **API Externe** : Intégration avec des systèmes tiers
5. **Workflow** : Gestion avancée des états de commandes
