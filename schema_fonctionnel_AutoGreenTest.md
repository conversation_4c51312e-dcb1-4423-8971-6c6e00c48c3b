# Schéma Fonctionnel de l'Application AutoGreenTest

## Vue d'ensemble
AutoGreenTest est une application d'automatisation des tests pour un système de gestion de stock. Elle utilise Spring Boot avec Cucumber pour les tests BDD (Behavior Driven Development) et communique avec une API REST externe via WebClient.

## Architecture Générale

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           APPLICATION AUTOGREENTEST                             │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────────┐  │
│  │   CUCUMBER BDD  │    │  SPRING BOOT    │    │     WEB REPOSITORIES        │  │
│  │   TEST LAYER    │◄──►│   FRAMEWORK     │◄──►│    (API CLIENTS)            │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────────────────┘  │
│           │                       │                           │                 │
│           │                       │                           │                 │
│           ▼                       ▼                           ▼                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────────┐  │
│  │ STEP DEFINITIONS│    │   EXECUTION     │    │    AUTHENTICATION          │  │
│  │   & FEATURES    │    │    CONTEXT      │    │      SYSTEM                 │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    │ HTTP/REST API
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        API GESTION-STOCK EXTERNE                               │
│                         (http://localhost:8081)                                │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Composants Principaux

### 1. Couche de Tests BDD (Cucumber)

#### Features Disponibles
- **creationArticle.feature** : Tests de création d'articles
- **validationArticle.feature** : Tests de validation des données d'articles
- **validationClient.feature** : Tests de validation des données clients
- **creationClient.feature** : Tests de création de clients
- **creationFournisseur.feature** : Tests de création de fournisseurs
- **validationFournisseur.feature** : Tests de validation des fournisseurs
- **creationCommandeClient.feature** : Tests de création de commandes clients
- **creationCommandeFournisseur.feature** : Tests de création de commandes fournisseurs

#### Step Definitions
```
┌─────────────────────────────────────────────────────────────────┐
│                    STEP DEFINITIONS                             │
├─────────────────────────────────────────────────────────────────┤
│  • ArticleStepDefinition                                       │
│  • ClientStepDefinition                                        │
│  • FournisseurStepDefinition                                   │
│  • CommandeClientStepDefinition                                │
│  • CommandeFournisseurStepDefinition                           │
│  • VenteStepDefinition                                         │
│  • AbstractStepDefinition (classe de base)                     │
│  • ContextResetHook (gestion du contexte)                      │
└─────────────────────────────────────────────────────────────────┘
```

### 2. Modèles de Données (DTOs)

```
┌─────────────────────────────────────────────────────────────────┐
│                      MODÈLES MÉTIER                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐  │
│  │ ArticleDto  │    │ ClientDto   │    │ CommandeClientDto   │  │
│  │ - id        │    │ - id        │    │ - id                │  │
│  │ - code      │    │ - nom       │    │ - code              │  │
│  │ - design.   │    │ - prenom    │    │ - dateCommande      │  │
│  │ - prixHT    │    │ - adresse   │    │ - etatCommande      │  │
│  │ - prixTTC   │    │ - photo     │    │ - client            │  │
│  │ - tauxTva   │    │ - mail      │    │ - ligneCommandes    │  │
│  │ - photo     │    │ - numTel    │    └─────────────────────┘  │
│  │ - categorie │    └─────────────┘                            │
│  └─────────────┘                                               │
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐  │
│  │FournisseurDto│   │ VenteDto    │    │CommandeFournisseurDto│ │
│  │ - id        │    │ - id        │    │ - id                │  │
│  │ - nom       │    │ - code      │    │ - code              │  │
│  │ - prenom    │    │ - dateVente │    │ - dateCommande      │  │
│  │ - adresse   │    │ - comment.  │    │ - etatCommande      │  │
│  │ - photo     │    │ - ligneVent.│    │ - fournisseur       │  │
│  │ - mail      │    └─────────────┘    │ - ligneCommandes    │  │
│  │ - numTel    │                       └─────────────────────┘  │
│  └─────────────┘                                               │
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐  │
│  │ AdresseDto  │    │CategorieDto │    │ MvtStkDto           │  │
│  │ - adresse1  │    │ - id        │    │ - id                │  │
│  │ - adresse2  │    │ - code      │    │ - dateMvt           │  │
│  │ - ville     │    │ - design.   │    │ - quantite          │  │
│  │ - pays      │    └─────────────┘    │ - article           │  │
│  │ - codePost. │                       │ - typeMvtStk        │  │
│  └─────────────┘                       │ - sourceMvtStk      │  │
│                                         └─────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 3. Couche de Communication (Web Repositories)

```
┌─────────────────────────────────────────────────────────────────┐
│                    WEB REPOSITORIES                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ AuthenticationRepo  │    │        API ENDPOINTS            │  │
│  │ - getToken()        │    │ • POST /articles                │  │
│  └─────────────────────┘    │ • POST /clients                 │  │
│             │                │ • POST /fournisseurs            │  │
│             │                │ • POST /commandesclients        │  │
│             ▼                │ • POST /commandesfournisseurs   │  │
│  ┌─────────────────────┐    │ • POST /ventes                  │  │
│  │   JWT TOKEN         │    │ • POST /categories              │  │
│  │   MANAGEMENT        │    │ • GET  /utilisateurs/account    │  │
│  └─────────────────────┘    └─────────────────────────────────┘  │
│             │                                                   │
│             ▼                                                   │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ ArticleRepository   │    │ CommandeClientRepository        │  │
│  │ ClientRepository    │    │ CommandeFournisseurRepository   │  │
│  │ FournisseurRepo     │    │ VenteRepository                 │  │
│  │ CategorieRepository │    │ MvtStkRepository                │  │
│  │ EntrepriseRepo      │    │ AccountManagerRepository        │  │
│  └─────────────────────┘    └─────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 4. Système d'Authentification

```
┌─────────────────────────────────────────────────────────────────┐
│                   AUTHENTIFICATION JWT                         │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────┐                                        │
│  │ Configuration       │                                        │
│  │ - URL Root          │ ──► http://localhost:8081/api/v1       │
│  │ - URL Auth          │ ──► /authenticate                      │
│  │ - Username          │ ──► admin / <EMAIL>              │
│  │ - Password          │ ──► admin / test1234                   │
│  └─────────────────────┘                                        │
│             │                                                   │
│             ▼                                                   │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ AuthenticationReq   │    │ AuthenticationResponse          │  │
│  │ - login             │◄──►│ - accessToken                   │  │
│  │ - password          │    │ - tokenType                     │  │
│  └─────────────────────┘    └─────────────────────────────────┘  │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │              HEADERS HTTP                                   │  │
│  │ Authorization: Bearer <JWT_TOKEN>                           │  │
│  │ Content-Type: application/json                              │  │
│  │ Accept: application/json                                    │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 5. Contexte d'Exécution

```
┌─────────────────────────────────────────────────────────────────┐
│                   EXECUTION CONTEXT                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ ExecutionContext    │    │        CONTEXT HOOKS            │  │
│  │ - context: Map      │    │ @Before: MockitoAnnotations     │  │
│  │ - addToContext()    │    │ @After: context.clear()         │  │
│  │ - getFromContext()  │    └─────────────────────────────────┘  │
│  └─────────────────────┘                                        │
│             │                                                   │
│             ▼                                                   │
│  ┌─────────────────────────────────────────────────────────────┐  │
│  │              DONNÉES PARTAGÉES                              │  │
│  │ • Objets créés pendant les tests                           │  │
│  │ • Références contextuelles (referenceIdContexte)           │  │
│  │ • Résultats de validation                                  │  │
│  │ • Données générées (Faker)                                 │  │
│  └─────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Flux de Données

### 1. Flux de Test Typique

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   GIVEN     │───►│    WHEN     │───►│    THEN     │───►│   RESULT    │
│ Préparation │    │  Exécution  │    │ Vérification│    │  Rapport    │
│ des données │    │  de l'action│    │ du résultat │    │   Cucumber  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ buildObject │    │ Repository  │    │ Assertions  │    │ JSON Report │
│ + Context   │    │ API Call    │    │ + Context   │    │ + HTML      │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### 2. Flux d'Authentification

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Test Start  │───►│ Get Token   │───►│ API Call    │───►│ Response    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                          │                   │
                          ▼                   │
                   ┌─────────────┐           │
                   │ JWT Token   │───────────┘
                   │ in Header   │
                   └─────────────┘
```

## Configuration et Déploiement

### Propriétés de Configuration
```properties
# Application
spring.application.name=AutoGreenTest

# JWT Configuration
application.jwt.url-root=http://localhost:8081/api/v1
application.jwt.url-auth=http://localhost:8081/api/v1/authenticate
application.jwt.username=admin
application.jwt.password=admin
```

### Dépendances Principales
- **Spring Boot 3.5.0** : Framework principal
- **Cucumber 7.11.1** : Tests BDD
- **WebClient** : Communication HTTP réactive
- **Jackson** : Sérialisation JSON
- **Lombok** : Réduction du code boilerplate
- **JavaFaker** : Génération de données de test

### Rapports de Tests
- **Format JSON** : `target/cucumber-reports/cucumber.json`
- **Format HTML** : Généré par maven-cucumber-reporting
- **Exécution parallèle** : 5 threads configurés

## Points d'Extension

### Nouveaux Tests
1. Ajouter de nouvelles features dans `src/test/resources/com.skysoft.app.bdd/`
2. Créer les step definitions correspondantes
3. Étendre AbstractStepDefinition si nécessaire

### Nouveaux Endpoints
1. Créer un nouveau Repository dans `com.skysoft.app.web`
2. Ajouter les DTOs correspondants
3. Intégrer dans AbstractStepDefinition

### Validation Avancée
1. Étendre les méthodes de validation dans les step definitions
2. Ajouter de nouveaux scénarios de validation
3. Utiliser les patterns BDD existants

Ce schéma fonctionnel montre une architecture bien structurée pour l'automatisation des tests d'une API de gestion de stock, utilisant les meilleures pratiques BDD avec Cucumber et Spring Boot.
